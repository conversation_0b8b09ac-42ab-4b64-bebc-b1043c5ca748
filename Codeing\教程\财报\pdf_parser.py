"""
PDF解析核心模块
基于MinerU实现PDF文档解析功能
"""

import copy
import json
import os
import time
from pathlib import Path
from typing import List, Optional, Tuple
from loguru import logger

try:
    from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
    from mineru.data.data_reader_writer import FileBasedDataWriter
    from mineru.utils.draw_bbox import draw_layout_bbox, draw_span_bbox
    from mineru.utils.enum_class import MakeMode
    from mineru.backend.vlm.vlm_analyze import doc_analyze as vlm_doc_analyze
    from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
    from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
    from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
    from mineru.backend.vlm.vlm_middle_json_mkcontent import union_make as vlm_union_make
    MINERU_AVAILABLE = True
except ImportError as e:
    logger.error(f"MinerU导入失败: {e}")
    logger.error("请先安装MinerU: pip install mineru[core]")
    MINERU_AVAILABLE = False

from config import config

class PDFParser:
    """PDF解析器"""
    
    def __init__(self):
        """初始化解析器"""
        if not MINERU_AVAILABLE:
            raise ImportError("MinerU未正确安装，请先安装: pip install mineru[core]")
        
        # 设置环境变量
        config.setup_environment()
        
        # 配置日志
        if config.verbose:
            logger.add("pdf_parser.log", rotation="10 MB", level="DEBUG")
        else:
            logger.add("pdf_parser.log", rotation="10 MB", level="INFO")
    
    def parse_single_pdf(
        self, 
        pdf_path: Path, 
        output_dir: Optional[Path] = None
    ) -> Tuple[bool, str]:
        """
        解析单个PDF文件
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录，如果为None则使用配置中的输出目录
            
        Returns:
            (是否成功, 消息)
        """
        try:
            if not pdf_path.exists():
                return False, f"PDF文件不存在: {pdf_path}"
            
            if output_dir is None:
                output_dir = config.output_dir
            
            logger.info(f"开始解析PDF: {pdf_path.name}")
            start_time = time.time()
            
            # 读取PDF文件
            pdf_bytes = read_fn(str(pdf_path))
            pdf_file_name = pdf_path.stem
            
            # 执行解析
            success = self._do_parse(
                output_dir=output_dir,
                pdf_file_names=[pdf_file_name],
                pdf_bytes_list=[pdf_bytes],
                p_lang_list=[config.ocr_language]
            )
            
            elapsed_time = time.time() - start_time
            
            if success:
                message = f"解析完成: {pdf_path.name} (耗时: {elapsed_time:.2f}秒)"
                logger.info(message)
                return True, message
            else:
                message = f"解析失败: {pdf_path.name}"
                logger.error(message)
                return False, message
                
        except Exception as e:
            error_msg = f"解析PDF时发生错误: {pdf_path.name} - {str(e)}"
            logger.exception(error_msg)
            return False, error_msg
    
    def parse_multiple_pdfs(
        self, 
        pdf_paths: List[Path], 
        output_dir: Optional[Path] = None
    ) -> List[Tuple[str, bool, str]]:
        """
        解析多个PDF文件
        
        Args:
            pdf_paths: PDF文件路径列表
            output_dir: 输出目录
            
        Returns:
            [(文件名, 是否成功, 消息), ...]
        """
        results = []
        
        for pdf_path in pdf_paths:
            success, message = self.parse_single_pdf(pdf_path, output_dir)
            results.append((pdf_path.name, success, message))
        
        return results
    
    def _do_parse(
        self,
        output_dir: Path,
        pdf_file_names: List[str],
        pdf_bytes_list: List[bytes],
        p_lang_list: List[str]
    ) -> bool:
        """
        执行PDF解析的核心逻辑
        
        Args:
            output_dir: 输出目录
            pdf_file_names: PDF文件名列表
            pdf_bytes_list: PDF字节数据列表
            p_lang_list: 语言列表
            
        Returns:
            是否成功
        """
        try:
            backend = config.parse_backend
            
            if backend == "pipeline":
                return self._parse_with_pipeline(
                    output_dir, pdf_file_names, pdf_bytes_list, p_lang_list
                )
            elif backend.startswith("vlm-"):
                return self._parse_with_vlm(
                    output_dir, pdf_file_names, pdf_bytes_list, backend
                )
            else:
                logger.error(f"不支持的后端: {backend}")
                return False
                
        except Exception as e:
            logger.exception(f"解析过程中发生错误: {e}")
            return False
    
    def _parse_with_pipeline(
        self,
        output_dir: Path,
        pdf_file_names: List[str],
        pdf_bytes_list: List[bytes],
        p_lang_list: List[str]
    ) -> bool:
        """使用pipeline后端解析"""
        try:
            # 转换PDF页面范围
            for idx, pdf_bytes in enumerate(pdf_bytes_list):
                new_pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(
                    pdf_bytes, config.start_page_id, config.end_page_id
                )
                pdf_bytes_list[idx] = new_pdf_bytes
            
            # 执行解析
            infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
                pdf_bytes_list, 
                p_lang_list, 
                parse_method=config.parse_method,
                formula_enable=config.formula_enable,
                table_enable=config.table_enable
            )
            
            # 处理解析结果
            for idx, model_list in enumerate(infer_results):
                self._save_pipeline_results(
                    output_dir, pdf_file_names[idx], model_list,
                    all_image_lists[idx], all_pdf_docs[idx],
                    lang_list[idx], ocr_enabled_list[idx],
                    pdf_bytes_list[idx]
                )
            
            return True
            
        except Exception as e:
            logger.exception(f"Pipeline解析失败: {e}")
            return False
    
    def _parse_with_vlm(
        self,
        output_dir: Path,
        pdf_file_names: List[str],
        pdf_bytes_list: List[bytes],
        backend: str
    ) -> bool:
        """使用VLM后端解析"""
        try:
            backend = backend[4:]  # 移除"vlm-"前缀
            
            for idx, pdf_bytes in enumerate(pdf_bytes_list):
                pdf_file_name = pdf_file_names[idx]
                
                # 转换PDF页面范围
                pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(
                    pdf_bytes, config.start_page_id, config.end_page_id
                )
                
                # 准备输出目录
                local_image_dir, local_md_dir = prepare_env(
                    str(output_dir), pdf_file_name, "vlm"
                )
                image_writer = FileBasedDataWriter(local_image_dir)
                md_writer = FileBasedDataWriter(local_md_dir)
                
                # 执行VLM解析
                middle_json, infer_result = vlm_doc_analyze(
                    pdf_bytes,
                    image_writer=image_writer,
                    backend=backend,
                    server_url=config.sglang_server_url
                )
                
                # 保存VLM解析结果
                self._save_vlm_results(
                    pdf_file_name, middle_json, infer_result,
                    pdf_bytes, local_image_dir, local_md_dir,
                    image_writer, md_writer
                )
            
            return True
            
        except Exception as e:
            logger.exception(f"VLM解析失败: {e}")
            return False

    def _save_pipeline_results(
        self,
        output_dir: Path,
        pdf_file_name: str,
        model_list: list,
        images_list: list,
        pdf_doc,
        lang: str,
        ocr_enable: bool,
        pdf_bytes: bytes
    ):
        """保存pipeline解析结果"""
        try:
            # 准备输出目录
            local_image_dir, local_md_dir = prepare_env(
                str(output_dir), pdf_file_name, config.parse_method
            )
            image_writer = FileBasedDataWriter(local_image_dir)
            md_writer = FileBasedDataWriter(local_md_dir)

            # 转换为中间JSON格式
            middle_json = pipeline_result_to_middle_json(
                model_list, images_list, pdf_doc, image_writer,
                lang, ocr_enable, config.formula_enable
            )

            pdf_info = middle_json["pdf_info"]

            # 绘制边界框
            if config.draw_layout_bbox:
                draw_layout_bbox(
                    pdf_info, pdf_bytes, local_md_dir,
                    f"{pdf_file_name}_layout.pdf"
                )

            if config.draw_span_bbox:
                draw_span_bbox(
                    pdf_info, pdf_bytes, local_md_dir,
                    f"{pdf_file_name}_span.pdf"
                )

            # 保存原始PDF
            if config.output_original_pdf:
                md_writer.write(f"{pdf_file_name}_origin.pdf", pdf_bytes)

            # 保存Markdown
            if config.output_markdown:
                image_dir = str(os.path.basename(local_image_dir))
                md_content_str = pipeline_union_make(
                    pdf_info, MakeMode.MM_MD, image_dir
                )
                md_writer.write_string(f"{pdf_file_name}.md", md_content_str)

            # 保存内容列表
            if config.output_content_list:
                image_dir = str(os.path.basename(local_image_dir))
                content_list = pipeline_union_make(
                    pdf_info, MakeMode.CONTENT_LIST, image_dir
                )
                md_writer.write_string(
                    f"{pdf_file_name}_content_list.json",
                    json.dumps(content_list, ensure_ascii=False, indent=4)
                )

            # 保存中间JSON
            if config.output_middle_json:
                md_writer.write_string(
                    f"{pdf_file_name}_middle.json",
                    json.dumps(middle_json, ensure_ascii=False, indent=4)
                )

            # 保存模型输出
            if config.output_model_output:
                model_json = copy.deepcopy(model_list)
                md_writer.write_string(
                    f"{pdf_file_name}_model.json",
                    json.dumps(model_json, ensure_ascii=False, indent=4)
                )

            logger.info(f"Pipeline结果已保存到: {local_md_dir}")

        except Exception as e:
            logger.exception(f"保存pipeline结果失败: {e}")

    def _save_vlm_results(
        self,
        pdf_file_name: str,
        middle_json: dict,
        infer_result: list,
        pdf_bytes: bytes,
        local_image_dir: str,
        local_md_dir: str,
        image_writer,
        md_writer
    ):
        """保存VLM解析结果"""
        try:
            pdf_info = middle_json["pdf_info"]

            # 绘制边界框
            if config.draw_layout_bbox:
                draw_layout_bbox(
                    pdf_info, pdf_bytes, local_md_dir,
                    f"{pdf_file_name}_layout.pdf"
                )

            # 保存原始PDF
            if config.output_original_pdf:
                md_writer.write(f"{pdf_file_name}_origin.pdf", pdf_bytes)

            # 保存Markdown
            if config.output_markdown:
                image_dir = str(os.path.basename(local_image_dir))
                md_content_str = vlm_union_make(
                    pdf_info, MakeMode.MM_MD, image_dir
                )
                md_writer.write_string(f"{pdf_file_name}.md", md_content_str)

            # 保存内容列表
            if config.output_content_list:
                image_dir = str(os.path.basename(local_image_dir))
                content_list = vlm_union_make(
                    pdf_info, MakeMode.CONTENT_LIST, image_dir
                )
                md_writer.write_string(
                    f"{pdf_file_name}_content_list.json",
                    json.dumps(content_list, ensure_ascii=False, indent=4)
                )

            # 保存中间JSON
            if config.output_middle_json:
                md_writer.write_string(
                    f"{pdf_file_name}_middle.json",
                    json.dumps(middle_json, ensure_ascii=False, indent=4)
                )

            # 保存模型输出
            if config.output_model_output:
                model_output = ("\n" + "-" * 50 + "\n").join(infer_result)
                md_writer.write_string(
                    f"{pdf_file_name}_model_output.txt", model_output
                )

            logger.info(f"VLM结果已保存到: {local_md_dir}")

        except Exception as e:
            logger.exception(f"保存VLM结果失败: {e}")
