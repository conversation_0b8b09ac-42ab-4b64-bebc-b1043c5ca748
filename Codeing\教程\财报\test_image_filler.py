#!/usr/bin/env python3
"""
图片描述填充工具测试脚本

用于测试主要功能是否正常工作。
"""

import os
import tempfile
from pathlib import Path
from image_description_filler import ImageDescriptionFiller
import shutil


def create_test_files():
    """创建测试用的文件和目录结构"""
    # 创建临时目录
    temp_dir = Path(tempfile.mkdtemp())
    parser_dir = temp_dir / "parser"
    parser_dir.mkdir()
    
    # 创建子目录
    subdir1 = parser_dir / "company_a"
    subdir2 = parser_dir / "company_b"
    subdir1.mkdir()
    subdir2.mkdir()
    
    # 创建测试用的Markdown文件
    md_content1 = """# 公司A财报分析
    
## 营收情况
公司A在2023年表现优异：

![](revenue_chart.jpg)

## 资产负债
资产负债情况如下：

![](balance_sheet.png)

## 现金流
现金流分析：

![](cash_flow.gif)

这里是一个已经有描述的图片：
![现有的图片描述](existing_image.jpg)
"""
    
    md_content2 = """# 公司B季度报告

![](quarterly_report.jpeg)

![](profit_analysis.bmp)
"""
    
    # 写入测试文件
    (subdir1 / "report.md").write_text(md_content1, encoding='utf-8')
    (subdir2 / "summary.md").write_text(md_content2, encoding='utf-8')
    
    # 创建一些虚拟图片文件（空文件，仅用于测试路径检查）
    (subdir1 / "revenue_chart.jpg").touch()
    (subdir1 / "balance_sheet.png").touch()
    (subdir1 / "cash_flow.gif").touch()
    (subdir1 / "existing_image.jpg").touch()
    (subdir2 / "quarterly_report.jpeg").touch()
    (subdir2 / "profit_analysis.bmp").touch()
    
    print(f"创建测试目录: {temp_dir}")
    print(f"Parser目录: {parser_dir}")
    
    return temp_dir, parser_dir


def test_pattern_matching():
    """测试正则表达式模式匹配"""
    print("\n=== 测试正则表达式模式匹配 ===")
    
    # 模拟一个简单的填充器（不需要实际API）
    class MockFiller:
        def __init__(self):
            import re
            self.empty_image_pattern = re.compile(r'!\[\]\(([^)]+\.(jpg|jpeg|png|gif|bmp|tiff|webp))\)', re.IGNORECASE)
        
        def find_empty_image_references(self, content, md_file_path):
            matches = []
            for match in self.empty_image_pattern.finditer(content):
                full_match = match.group(0)
                image_filename = match.group(1)
                image_path = md_file_path.parent / image_filename
                matches.append((full_match, image_filename, image_path))
            return matches
    
    mock_filler = MockFiller()
    
    test_content = """
# 测试文档

![](test1.jpg)
![已有描述](test2.png)
![](test3.jpeg)
![](test4.GIF)
![](test5.BMP)
![](invalid.txt)
![](test6.webp)
"""
    
    test_path = Path("/fake/path/test.md")
    matches = mock_filler.find_empty_image_references(test_content, test_path)
    
    print(f"找到 {len(matches)} 个匹配项:")
    for full_match, filename, path in matches:
        print(f"  {full_match} -> {filename}")
    
    # 预期应该找到: test1.jpg, test3.jpeg, test4.GIF, test5.BMP, test6.webp
    expected_count = 5
    if len(matches) == expected_count:
        print("✅ 正则表达式测试通过")
    else:
        print(f"❌ 正则表达式测试失败，预期 {expected_count} 个匹配，实际 {len(matches)} 个")


def test_file_scanning():
    """测试文件扫描功能"""
    print("\n=== 测试文件扫描功能 ===")
    
    temp_dir, parser_dir = create_test_files()
    
    try:
        # 测试不需要API的基本功能
        class MockFiller:
            def scan_markdown_files(self, parser_dir):
                parser_path = Path(parser_dir)
                markdown_files = []
                for md_file in parser_path.rglob("*.md"):
                    markdown_files.append(md_file)
                return markdown_files
        
        mock_filler = MockFiller()
        md_files = mock_filler.scan_markdown_files(str(parser_dir))
        
        print(f"找到 {len(md_files)} 个Markdown文件:")
        for md_file in md_files:
            print(f"  {md_file}")
        
        if len(md_files) == 2:
            print("✅ 文件扫描测试通过")
        else:
            print(f"❌ 文件扫描测试失败，预期 2 个文件，实际 {len(md_files)} 个")
    
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir)


def test_empty_reference_detection():
    """测试空图片引用检测"""
    print("\n=== 测试空图片引用检测 ===")
    
    temp_dir, parser_dir = create_test_files()
    
    try:
        class MockFiller:
            def __init__(self):
                import re
                self.empty_image_pattern = re.compile(r'!\[\]\(([^)]+\.(jpg|jpeg|png|gif|bmp|tiff|webp))\)', re.IGNORECASE)
            
            def find_empty_image_references(self, content, md_file_path):
                matches = []
                for match in self.empty_image_pattern.finditer(content):
                    full_match = match.group(0)
                    image_filename = match.group(1)
                    image_path = md_file_path.parent / image_filename
                    if image_path.exists():
                        matches.append((full_match, image_filename, image_path))
                return matches
        
        mock_filler = MockFiller()
        
        # 读取测试文件
        test_md = parser_dir / "company_a" / "report.md"
        with open(test_md, 'r', encoding='utf-8') as f:
            content = f.read()
        
        matches = mock_filler.find_empty_image_references(content, test_md)
        
        print(f"在 {test_md.name} 中找到 {len(matches)} 个空图片引用:")
        for full_match, filename, path in matches:
            print(f"  {filename} ({'存在' if path.exists() else '不存在'})")
        
        # 预期找到3个空引用：revenue_chart.jpg, balance_sheet.png, cash_flow.gif
        if len(matches) == 3:
            print("✅ 空图片引用检测测试通过")
        else:
            print(f"❌ 空图片引用检测测试失败，预期 3 个引用，实际 {len(matches)} 个")
    
    finally:
        shutil.rmtree(temp_dir)


def main():
    """运行所有测试"""
    print("开始运行图片描述填充工具测试...")
    
    test_pattern_matching()
    test_file_scanning()
    test_empty_reference_detection()
    
    print("\n=== 测试完成 ===")
    print("注意：这些测试只验证了基本功能，不包括实际的API调用。")
    print("要测试完整功能，请：")
    print("1. 配置 .env 文件")
    print("2. 使用 --dry-run 参数运行主脚本")


if __name__ == "__main__":
    main()