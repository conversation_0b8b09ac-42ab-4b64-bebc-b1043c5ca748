"""
图片描述填充脚本

扫描 data/parser/ 目录下所有 Markdown 文件，找到空的图片引用，
使用硅基流动平台的视觉模型API生成图片描述并填充到方括号中。

基于 MarkEverythingDown 项目的架构实现。
"""

import os
import re
import base64
from pathlib import Path
from typing import List, Tuple, Optional
from dotenv import load_dotenv
from openai import OpenAI
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('image_description.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ImageDescriptionFiller:
    """图片描述填充器"""
    
    def __init__(self, api_key: str, base_url: str, model: str, temperature: float = 0.1):
        """
        初始化图片描述填充器
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model: 模型名称
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.temperature = temperature
        
        # 配置 OpenAI 客户端
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        # 支持的图片格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
        
        # 用于匹配空图片引用的正则表达式
        self.empty_image_pattern = re.compile(r'!\[\]\(([^)]+\.(jpg|jpeg|png|gif|bmp|tiff|webp))\)', re.IGNORECASE)
        
        logger.info(f"初始化图片描述填充器 - 模型: {self.model}, API: {self.base_url}")
    
    def scan_markdown_files(self, parser_dir: str) -> List[Path]:
        """
        扫描指定目录下的所有 Markdown 文件
        
        Args:
            parser_dir: parser目录路径
            
        Returns:
            List[Path]: Markdown 文件路径列表
        """
        parser_path = Path(parser_dir)
        if not parser_path.exists():
            logger.error(f"目录不存在: {parser_dir}")
            return []
        
        markdown_files = []
        for md_file in parser_path.rglob("*.md"):
            markdown_files.append(md_file)
            
        logger.info(f"找到 {len(markdown_files)} 个 Markdown 文件")
        return markdown_files
    
    def find_empty_image_references(self, content: str, md_file_path: Path) -> List[Tuple[str, str, Path]]:
        """
        在 Markdown 内容中找到所有空的图片引用
        
        Args:
            content: Markdown 文件内容
            md_file_path: Markdown 文件路径
            
        Returns:
            List[Tuple[str, str, Path]]: (完整匹配文本, 图片文件名, 图片完整路径) 的列表
        """
        matches = []
        for match in self.empty_image_pattern.finditer(content):
            full_match = match.group(0)  # 完整的匹配文本 ![](xxx.jpg)
            image_filename = match.group(1)  # 图片文件名 xxx.jpg
            
            # 构建图片完整路径（图片在 Markdown 文件同目录下）
            image_path = md_file_path.parent / image_filename
            
            if image_path.exists():
                matches.append((full_match, image_filename, image_path))
                logger.debug(f"找到空图片引用: {image_filename}")
            else:
                logger.warning(f"图片文件不存在: {image_path}")
        
        return matches
    
    def encode_image_to_base64(self, image_path: Path) -> str:
        """
        将图片编码为 base64 字符串
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            str: base64 编码的图片数据
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode("utf-8")
        except Exception as e:
            logger.error(f"编码图片失败 {image_path}: {str(e)}")
            raise
    
    def generate_image_description(self, image_path: Path) -> str:
        """
        使用视觉模型生成图片描述
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            str: 图片描述
        """
        try:
            logger.info(f"正在为图片生成描述: {image_path.name}")
            
            # 编码图片
            image_base64 = self.encode_image_to_base64(image_path)
            
            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的财报分析助手。请仔细分析这张图片，提供准确、详细的中文描述。

描述要求：
1. 重点关注财务数据、图表、表格等关键信息
2. 描述应该简洁明了，适合后续的RAG检索使用
3. 如果是图表，请说明图表类型和主要数据趋势
4. 如果是表格，请概括主要数据类别
5. 如果包含文字，请提取关键信息
6. 描述长度控制在50-120字之间

请直接输出描述文字，不要包含"这张图片显示"等开头语句。"""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请为这张财报相关的图片提供专业的中文描述："
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}
                        }
                    ]
                }
            ]
            
            # 调用API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature
            )
            
            description = response.choices[0].message.content.strip()
            logger.info(f"生成描述成功: {description[:50]}...")
            return description
            
        except Exception as e:
            logger.error(f"生成图片描述失败 {image_path}: {str(e)}")
            return f"图片描述生成失败: {image_path.name}"
    
    def update_markdown_content(self, content: str, image_references: List[Tuple[str, str, Path]]) -> str:
        """
        更新 Markdown 内容，填充图片描述
        
        Args:
            content: 原始 Markdown 内容
            image_references: 图片引用列表
            
        Returns:
            str: 更新后的 Markdown 内容
        """
        updated_content = content
        
        for i, (full_match, image_filename, image_path) in enumerate(image_references, 1):
            try:
                logger.info(f"处理图片 {i}/{len(image_references)}: {image_filename}")
                # 生成图片描述
                description = self.generate_image_description(image_path)

                # 替换空的图片引用
                new_reference = f"![{description}]({image_filename})"
                updated_content = updated_content.replace(full_match, new_reference)

                logger.info(f"已更新图片引用 {i}/{len(image_references)}: {image_filename}")

            except Exception as e:
                logger.error(f"处理图片引用失败 {image_filename}: {str(e)}")
                # 如果生成失败，至少填入文件名作为描述
                fallback_reference = f"![{image_filename}]({image_filename})"
                updated_content = updated_content.replace(full_match, fallback_reference)
                logger.info(f"使用备用描述 {i}/{len(image_references)}: {image_filename}")
        
        return updated_content
    
    def process_markdown_file(self, md_file_path: Path, dry_run: bool = False) -> bool:
        """
        处理单个 Markdown 文件
        
        Args:
            md_file_path: Markdown 文件路径
            dry_run: 是否为试运行模式（不实际修改文件）
            
        Returns:
            bool: 是否成功处理
        """
        try:
            logger.info(f"处理文件: {md_file_path}")
            
            # 读取文件内容
            with open(md_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找空的图片引用
            image_references = self.find_empty_image_references(content, md_file_path)

            if not image_references:
                logger.info(f"文件中没有找到空的图片引用: {md_file_path}")
                return True

            logger.info(f"找到 {len(image_references)} 个空图片引用")

            # 如果图片引用太多，分批处理以避免超时
            if len(image_references) > 10:
                logger.info(f"图片数量较多({len(image_references)}个)，将分批处理")
            
            if dry_run:
                logger.info("试运行模式，不会实际修改文件")
                for _, image_filename, _ in image_references:
                    logger.info(f"  将处理: {image_filename}")
                return True
            
            # 更新内容
            logger.info("开始更新 Markdown 内容...")
            updated_content = self.update_markdown_content(content, image_references)
            logger.info(f"内容更新完成，原始长度: {len(content)}, 更新后长度: {len(updated_content)}")

            # 检查内容是否真的发生了变化
            if content == updated_content:
                logger.warning("警告：内容没有发生变化！")
                return False

            # 保存更新后的内容
            logger.info(f"正在保存更新后的内容到: {md_file_path}")
            with open(md_file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)

            logger.info(f"文件更新完成: {md_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"处理文件失败 {md_file_path}: {str(e)}")
            return False
    
    def process_all_files(self, parser_dir: str, dry_run: bool = False) -> None:
        """
        处理所有 Markdown 文件
        
        Args:
            parser_dir: parser目录路径
            dry_run: 是否为试运行模式
        """
        markdown_files = self.scan_markdown_files(parser_dir)
        
        if not markdown_files:
            logger.warning("没有找到任何 Markdown 文件")
            return
        
        success_count = 0
        total_count = len(markdown_files)
        
        for md_file in markdown_files:
            if self.process_markdown_file(md_file, dry_run):
                success_count += 1
        
        logger.info(f"处理完成: {success_count}/{total_count} 个文件成功")


def main():
    """主函数"""
    # 固定配置
    parser_dir = "data/parser"
    config_file = ".env"
    dry_run = False

    # 加载环境变量
    if not os.path.exists(config_file):
        logger.error(f"配置文件不存在: {config_file}")
        logger.info("请创建配置文件，参考 .env.template")
        return

    load_dotenv(config_file)

    # 获取配置
    api_key = os.getenv("VLM_API_KEY")
    base_url = os.getenv("VLM_BASE_URL", "https://api.siliconflow.cn/v1")
    model = os.getenv("VLM_MODEL", "Qwen/Qwen2.5-VL-72B-Instruct")
    temperature = float(os.getenv("VLM_TEMPERATURE", "0.1"))

    if not api_key:
        logger.error("未找到 VLM_API_KEY 环境变量")
        logger.info("请在配置文件中设置 VLM_API_KEY")
        return

    logger.info(f"使用配置文件: {config_file}")
    logger.info(f"处理目录: {parser_dir}")
    logger.info(f"模型: {model}")
    logger.info(f"API: {base_url}")

    # 创建填充器并处理文件
    try:
        filler = ImageDescriptionFiller(
            api_key=api_key,
            base_url=base_url,
            model=model,
            temperature=temperature
        )

        filler.process_all_files(parser_dir, dry_run)

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()