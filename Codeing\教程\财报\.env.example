# MinerU 财报PDF解析工具配置文件
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 模型源配置
# =============================================================================
# 模型下载源，可选值: huggingface, modelscope, local
# 如果网络访问huggingface有问题，建议使用modelscope
MINERU_MODEL_SOURCE=modelscope

# =============================================================================
# 解析后端配置
# =============================================================================
# 解析后端，可选值: pipeline, vlm-transformers, vlm-sglang-engine, vlm-sglang-client
# pipeline: 更通用，适合大多数情况
# vlm-*: 基于视觉语言模型，效果更好但需要更多资源
PARSE_BACKEND=pipeline

# 解析方法，可选值: auto, txt, ocr
# auto: 自动判断，txt: 文本提取，ocr: OCR识别
PARSE_METHOD=auto

# =============================================================================
# 语言配置
# =============================================================================
# OCR语言，可选值: ch, en, korean, japan, chinese_cht, ta, te, ka等
# ch: 中文简体，en: 英文
OCR_LANGUAGE=ch

# =============================================================================
# 功能开关
# =============================================================================
# 是否启用公式解析
FORMULA_ENABLE=true

# 是否启用表格解析
TABLE_ENABLE=true

# 是否绘制布局边界框
DRAW_LAYOUT_BBOX=true

# 是否绘制文本块边界框
DRAW_SPAN_BBOX=true

# =============================================================================
# 输出配置
# =============================================================================
# 是否输出Markdown文件
OUTPUT_MARKDOWN=true

# 是否输出中间JSON文件
OUTPUT_MIDDLE_JSON=true

# 是否输出模型原始输出
OUTPUT_MODEL_OUTPUT=true

# 是否保存原始PDF文件
OUTPUT_ORIGINAL_PDF=true

# 是否输出内容列表
OUTPUT_CONTENT_LIST=true

# =============================================================================
# 路径配置
# =============================================================================
# 输入PDF目录（相对于项目根目录）
INPUT_DIR=data/财报数据库

# 输出目录（相对于项目根目录）
OUTPUT_DIR=output

# =============================================================================
# 高级配置
# =============================================================================
# 起始页码（从0开始）
START_PAGE_ID=0

# 结束页码（None表示解析到最后一页）
END_PAGE_ID=

# 批量处理时的并发数
MAX_WORKERS=2

# 是否显示详细日志
VERBOSE=true

# =============================================================================
# API配置（如果使用在线API服务）
# =============================================================================
# MinerU在线API地址（如果使用）
# MINERU_API_URL=https://api.mineru.net

# API密钥（如果需要）
# MINERU_API_KEY=your_api_key_here

# =============================================================================
# SGLang配置（如果使用vlm-sglang-client后端）
# =============================================================================
# SGLang服务器地址
# SGLANG_SERVER_URL=http://127.0.0.1:30000
