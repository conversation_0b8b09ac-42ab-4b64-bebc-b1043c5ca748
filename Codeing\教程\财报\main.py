#!/usr/bin/env python3
"""
财报PDF解析工具主入口
基于MinerU实现的财报文档解析工具
"""

import argparse
import sys
from pathlib import Path
from loguru import logger

from config import config
from batch_processor import BatchProcessor
from pdf_parser import PDFParser

def setup_logging():
    """设置日志"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO" if not config.verbose else "DEBUG"
    )
    
    # 添加文件输出
    logger.add(
        "financial_report_parser.log",
        rotation="10 MB",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG"
    )

def parse_single_file(file_path: str, output_dir: str = None):
    """解析单个PDF文件"""
    pdf_path = Path(file_path)
    
    if not pdf_path.exists():
        logger.error(f"文件不存在: {file_path}")
        return False
    
    if not pdf_path.suffix.lower() == '.pdf':
        logger.error(f"不是PDF文件: {file_path}")
        return False
    
    # 创建解析器
    parser = PDFParser()
    
    # 设置输出目录
    if output_dir:
        output_path = Path(output_dir)
    else:
        output_path = config.output_dir
    
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 执行解析
    logger.info(f"开始解析文件: {pdf_path.name}")
    success, message = parser.parse_single_pdf(pdf_path, output_path)
    
    if success:
        logger.info(f"解析成功: {message}")
        return True
    else:
        logger.error(f"解析失败: {message}")
        return False

def parse_batch(input_dir: str = None, output_dir: str = None, multithread: bool = True):
    """批量解析PDF文件"""
    # 设置输入目录
    if input_dir:
        input_path = Path(input_dir)
    else:
        input_path = config.input_dir
    
    if not input_path.exists():
        logger.error(f"输入目录不存在: {input_path}")
        return False
    
    # 设置输出目录
    if output_dir:
        output_path = Path(output_dir)
    else:
        output_path = config.output_dir
    
    # 创建批量处理器
    processor = BatchProcessor()
    
    # 执行批量处理
    logger.info(f"开始批量处理目录: {input_path}")
    results = processor.process_all(
        input_dir=input_path,
        output_dir=output_path,
        use_multithread=multithread
    )
    
    # 显示结果摘要
    success_count = sum(1 for _, success, _ in results if success)
    total_count = len(results)
    
    if total_count > 0:
        logger.info(f"批量处理完成: {success_count}/{total_count} 成功")
        
        # 显示失败的文件
        failed_files = processor.get_failed_files()
        if failed_files:
            logger.warning(f"以下文件处理失败:")
            for filename in failed_files:
                logger.warning(f"  - {filename}")
        
        return success_count == total_count
    else:
        logger.warning("没有找到PDF文件")
        return False

def list_files(input_dir: str = None):
    """列出输入目录中的PDF文件"""
    if input_dir:
        input_path = Path(input_dir)
    else:
        input_path = config.input_dir
    
    if not input_path.exists():
        logger.error(f"输入目录不存在: {input_path}")
        return
    
    # 查找PDF文件
    pdf_files = list(input_path.glob("*.pdf"))
    pdf_files.extend(input_path.glob("*.PDF"))
    pdf_files.sort(key=lambda x: x.name)
    
    logger.info(f"在 {input_path} 中找到 {len(pdf_files)} 个PDF文件:")
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"{i:3d}. {pdf_file.name}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="财报PDF解析工具 - 基于MinerU",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 解析单个PDF文件
  python main.py single report.pdf
  
  # 批量解析目录中的所有PDF文件
  python main.py batch
  
  # 指定输入和输出目录
  python main.py batch -i /path/to/pdfs -o /path/to/output
  
  # 列出目录中的PDF文件
  python main.py list
  
  # 显示当前配置
  python main.py config
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 单文件解析命令
    single_parser = subparsers.add_parser('single', help='解析单个PDF文件')
    single_parser.add_argument('file', help='PDF文件路径')
    single_parser.add_argument('-o', '--output', help='输出目录')
    
    # 批量解析命令
    batch_parser = subparsers.add_parser('batch', help='批量解析PDF文件')
    batch_parser.add_argument('-i', '--input', help='输入目录')
    batch_parser.add_argument('-o', '--output', help='输出目录')
    batch_parser.add_argument('--no-multithread', action='store_true', help='禁用多线程')
    
    # 列出文件命令
    list_parser = subparsers.add_parser('list', help='列出目录中的PDF文件')
    list_parser.add_argument('-i', '--input', help='输入目录')
    
    # 配置显示命令
    config_parser = subparsers.add_parser('config', help='显示当前配置')
    
    # 解析参数
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    # 验证配置
    if not config.validate_config():
        logger.error("配置验证失败")
        return 1
    
    # 执行命令
    if args.command == 'single':
        success = parse_single_file(args.file, args.output)
        return 0 if success else 1
    
    elif args.command == 'batch':
        success = parse_batch(
            input_dir=args.input,
            output_dir=args.output,
            multithread=not args.no_multithread
        )
        return 0 if success else 1
    
    elif args.command == 'list':
        list_files(args.input)
        return 0
    
    elif args.command == 'config':
        config.print_config()
        return 0
    
    else:
        parser.print_help()
        return 1

if __name__ == '__main__':
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.exception(f"程序执行出错: {e}")
        sys.exit(1)
