"""
配置管理模块
负责加载和管理环境变量配置
"""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

class Config:
    """配置管理类"""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化配置
        
        Args:
            env_file: .env文件路径，如果为None则使用默认路径
        """
        # 加载.env文件
        if env_file is None:
            env_file = Path(__file__).parent / ".env"
        
        if Path(env_file).exists():
            load_dotenv(env_file)
            print(f"已加载配置文件: {env_file}")
        else:
            print(f"配置文件不存在: {env_file}")
            print("请复制 .env.example 为 .env 并配置相关参数")
    
    @property
    def model_source(self) -> str:
        """模型下载源"""
        return os.getenv("MINERU_MODEL_SOURCE", "modelscope")
    
    @property
    def parse_backend(self) -> str:
        """解析后端"""
        return os.getenv("PARSE_BACKEND", "pipeline")
    
    @property
    def parse_method(self) -> str:
        """解析方法"""
        return os.getenv("PARSE_METHOD", "auto")
    
    @property
    def ocr_language(self) -> str:
        """OCR语言"""
        return os.getenv("OCR_LANGUAGE", "ch")
    
    @property
    def formula_enable(self) -> bool:
        """是否启用公式解析"""
        return os.getenv("FORMULA_ENABLE", "true").lower() == "true"
    
    @property
    def table_enable(self) -> bool:
        """是否启用表格解析"""
        return os.getenv("TABLE_ENABLE", "true").lower() == "true"
    
    @property
    def draw_layout_bbox(self) -> bool:
        """是否绘制布局边界框"""
        return os.getenv("DRAW_LAYOUT_BBOX", "true").lower() == "true"
    
    @property
    def draw_span_bbox(self) -> bool:
        """是否绘制文本块边界框"""
        return os.getenv("DRAW_SPAN_BBOX", "true").lower() == "true"
    
    @property
    def output_markdown(self) -> bool:
        """是否输出Markdown文件"""
        return os.getenv("OUTPUT_MARKDOWN", "true").lower() == "true"
    
    @property
    def output_middle_json(self) -> bool:
        """是否输出中间JSON文件"""
        return os.getenv("OUTPUT_MIDDLE_JSON", "true").lower() == "true"
    
    @property
    def output_model_output(self) -> bool:
        """是否输出模型原始输出"""
        return os.getenv("OUTPUT_MODEL_OUTPUT", "true").lower() == "true"
    
    @property
    def output_original_pdf(self) -> bool:
        """是否保存原始PDF文件"""
        return os.getenv("OUTPUT_ORIGINAL_PDF", "true").lower() == "true"
    
    @property
    def output_content_list(self) -> bool:
        """是否输出内容列表"""
        return os.getenv("OUTPUT_CONTENT_LIST", "true").lower() == "true"
    
    @property
    def input_dir(self) -> Path:
        """输入PDF目录"""
        input_path = os.getenv("INPUT_DIR", "data/财报数据库")
        return Path(__file__).parent / input_path
    
    @property
    def output_dir(self) -> Path:
        """输出目录"""
        output_path = os.getenv("OUTPUT_DIR", "output")
        return Path(__file__).parent / output_path
    
    @property
    def start_page_id(self) -> int:
        """起始页码"""
        return int(os.getenv("START_PAGE_ID", "0"))
    
    @property
    def end_page_id(self) -> Optional[int]:
        """结束页码"""
        end_page = os.getenv("END_PAGE_ID", "")
        return int(end_page) if end_page else None
    
    @property
    def max_workers(self) -> int:
        """批量处理时的并发数"""
        return int(os.getenv("MAX_WORKERS", "2"))
    
    @property
    def verbose(self) -> bool:
        """是否显示详细日志"""
        return os.getenv("VERBOSE", "true").lower() == "true"
    
    @property
    def mineru_api_url(self) -> Optional[str]:
        """MinerU在线API地址"""
        return os.getenv("MINERU_API_URL")
    
    @property
    def mineru_api_key(self) -> Optional[str]:
        """API密钥"""
        return os.getenv("MINERU_API_KEY")
    
    @property
    def sglang_server_url(self) -> Optional[str]:
        """SGLang服务器地址"""
        return os.getenv("SGLANG_SERVER_URL")
    
    def setup_environment(self):
        """设置环境变量"""
        # 设置模型源
        os.environ["MINERU_MODEL_SOURCE"] = self.model_source
        
        # 如果有API密钥，设置相关环境变量
        if self.mineru_api_key:
            os.environ["MINERU_API_KEY"] = self.mineru_api_key
        
        print(f"模型源设置为: {self.model_source}")
        print(f"解析后端: {self.parse_backend}")
        print(f"解析方法: {self.parse_method}")
        print(f"OCR语言: {self.ocr_language}")
    
    def validate_config(self) -> bool:
        """验证配置是否有效"""
        # 检查输入目录是否存在
        if not self.input_dir.exists():
            print(f"错误: 输入目录不存在: {self.input_dir}")
            return False
        
        # 检查输入目录是否有PDF文件
        pdf_files = list(self.input_dir.glob("*.pdf"))
        if not pdf_files:
            print(f"警告: 输入目录中没有找到PDF文件: {self.input_dir}")
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        return True
    
    def print_config(self):
        """打印当前配置"""
        print("\n" + "="*50)
        print("当前配置:")
        print("="*50)
        print(f"模型源: {self.model_source}")
        print(f"解析后端: {self.parse_backend}")
        print(f"解析方法: {self.parse_method}")
        print(f"OCR语言: {self.ocr_language}")
        print(f"公式解析: {self.formula_enable}")
        print(f"表格解析: {self.table_enable}")
        print(f"输入目录: {self.input_dir}")
        print(f"输出目录: {self.output_dir}")
        print(f"并发数: {self.max_workers}")
        print("="*50 + "\n")

# 全局配置实例
config = Config()
