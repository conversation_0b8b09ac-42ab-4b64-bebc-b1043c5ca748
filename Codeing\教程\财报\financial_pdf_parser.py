#!/usr/bin/env python3
"""
财报PDF解析工具 - 基于MinerU在线API
简化版本，所有功能集成在一个文件中
"""

import os
import json
import time
import requests
from pathlib import Path
from typing import List, Dict, Any
from dotenv import load_dotenv
from tqdm import tqdm

# 加载环境变量
load_dotenv()

class FinancialPDFParser:
    """财报PDF解析器"""
    
    def __init__(self):
        # API配置
        self.api_url = "https://mineru.net/api/v4/file-urls/batch"
        self.api_key = os.getenv("MINERU_API_KEY", "")
        
        if not self.api_key or self.api_key == "your_api_key_here":
            raise ValueError("请在.env文件中设置有效的MINERU_API_KEY")
        
        # 请求头
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }
        
        # 解析配置
        self.config = {
            "enable_formula": os.getenv("FORMULA_ENABLE", "true").lower() == "true",
            "language": os.getenv("OCR_LANGUAGE", "ch"),
            "enable_table": os.getenv("TABLE_ENABLE", "true").lower() == "true",
            "model_version": os.getenv("MODEL_VERSION", "v2")
        }
        
        # 路径配置
        self.input_dir = Path(os.getenv("INPUT_DIR", "data/财报数据库"))
        self.output_dir = Path(os.getenv("OUTPUT_DIR", "output"))
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 网络配置
        self.max_retries = int(os.getenv("MAX_RETRIES", "3"))
        self.upload_timeout = int(os.getenv("UPLOAD_TIMEOUT", "60"))
        
        print(f"API配置: {self.config}")
        print(f"输入目录: {self.input_dir}")
        print(f"输出目录: {self.output_dir}")
    
    def find_pdf_files(self) -> List[Path]:
        """查找所有PDF文件"""
        if not self.input_dir.exists():
            print(f"错误: 输入目录不存在: {self.input_dir}")
            return []

        # 使用集合去重，避免重复计算.pdf和.PDF
        pdf_files_set = set()
        pdf_files_set.update(self.input_dir.glob("*.pdf"))
        pdf_files_set.update(self.input_dir.glob("*.PDF"))

        pdf_files = sorted(list(pdf_files_set))

        print(f"找到 {len(pdf_files)} 个PDF文件")
        return pdf_files
    
    def upload_and_parse_batch(self, pdf_files: List[Path]) -> bool:
        """批量上传并解析PDF文件"""
        if not pdf_files:
            print("没有找到PDF文件")
            return False

        # 准备文件信息
        files_info = []
        timestamp = int(time.time())
        for i, pdf_file in enumerate(pdf_files):
            files_info.append({
                "name": pdf_file.name,
                "is_ocr": True,
                "data_id": f"financial_report_{i}_{timestamp}"
            })

        # 准备请求数据
        data = {
            "enable_formula": self.config["enable_formula"],
            "language": self.config["language"],
            "enable_table": self.config["enable_table"],
            "files": files_info
        }

        try:
            print("正在申请上传链接...")
            response = requests.post(self.api_url, headers=self.headers, json=data)

            if response.status_code != 200:
                print(f"申请上传链接失败: {response.status_code} - {response.text}")
                return False

            result = response.json()

            if result.get("code") != 0:
                print(f"申请上传链接失败: {result.get('msg', '未知错误')}")
                return False

            batch_id = result["data"]["batch_id"]
            upload_urls = result["data"]["file_urls"]

            print(f"获得批次ID: {batch_id}")
            print(f"开始上传 {len(upload_urls)} 个文件...")

            # 上传文件
            success_count = 0
            with tqdm(total=len(pdf_files), desc="上传文件") as pbar:
                for i, (pdf_file, upload_url) in enumerate(zip(pdf_files, upload_urls)):
                    upload_success = self._upload_file_with_retry(pdf_file, upload_url, pbar)
                    if upload_success:
                        success_count += 1

                    pbar.update(1)

            print(f"上传完成: {success_count}/{len(pdf_files)} 成功")

            if success_count > 0:
                print("文件上传完成，系统将自动开始解析...")
                print(f"批次ID: {batch_id}")

                # 保存批次信息
                self.save_batch_info(batch_id, files_info)
                return True
            else:
                print("没有文件上传成功")
                return False

        except Exception as e:
            print(f"上传过程中出错: {e}")
            return False

    def _upload_file_with_retry(self, pdf_file: Path, upload_url: str, pbar) -> bool:
        """带重试机制的文件上传"""
        for attempt in range(self.max_retries):
            try:
                with open(pdf_file, 'rb') as f:
                    upload_response = requests.put(
                        upload_url,
                        data=f,
                        timeout=self.upload_timeout,
                        verify=True  # 启用SSL验证
                    )

                if upload_response.status_code == 200:
                    pbar.set_description(f"上传成功: {pdf_file.name}")
                    return True
                else:
                    error_msg = f"HTTP {upload_response.status_code}"
                    if attempt < self.max_retries - 1:
                        pbar.set_description(f"重试 {attempt+1}/{self.max_retries}: {pdf_file.name}")
                        time.sleep(2 ** attempt)  # 指数退避
                    else:
                        pbar.set_description(f"上传失败: {pdf_file.name}")
                        print(f"上传失败: {pdf_file.name} - {error_msg}")

            except (requests.exceptions.SSLError,
                    requests.exceptions.ConnectionError,
                    requests.exceptions.Timeout) as e:
                if attempt < self.max_retries - 1:
                    pbar.set_description(f"网络错误重试 {attempt+1}/{self.max_retries}: {pdf_file.name}")
                    time.sleep(2 ** attempt)  # 指数退避：2秒、4秒、8秒
                else:
                    pbar.set_description(f"上传失败: {pdf_file.name}")
                    print(f"上传文件时出错: {pdf_file.name} - {e}")

            except Exception as e:
                pbar.set_description(f"上传失败: {pdf_file.name}")
                print(f"上传文件时出错: {pdf_file.name} - {e}")
                break

        return False

    def save_batch_info(self, batch_id: str, files_info: List[Dict]):
        """保存批次信息"""
        batch_info = {
            "batch_id": batch_id,
            "timestamp": time.time(),
            "files": files_info,
            "config": self.config
        }

        batch_file = self.output_dir / f"batch_{batch_id}.json"
        with open(batch_file, 'w', encoding='utf-8') as f:
            json.dump(batch_info, f, ensure_ascii=False, indent=2)

        print(f"批次信息已保存到: {batch_file}")
    
    def check_task_status(self, batch_id: str, max_retries: int = 60, sleep_seconds: int = 5) -> dict:
        """
        检查批次任务状态

        Args:
            batch_id: 批次ID
            max_retries: 最大重试次数
            sleep_seconds: 重试间隔秒数

        Returns:
            dict: 包含任务状态信息的字典
        """
        retry_count = 0
        results_api = "https://mineru.net/api/v4/extract-results/batch"

        while retry_count < max_retries:
            retry_count += 1

            try:
                response = requests.get(
                    f"{results_api}/{batch_id}",
                    headers=self.headers,
                    timeout=60
                )

                if response.status_code != 200:
                    if retry_count < max_retries:
                        print(f"状态查询失败，{sleep_seconds}秒后重试... ({retry_count}/{max_retries})")
                        time.sleep(sleep_seconds)
                    continue

                status_data = response.json()
                task_data = status_data.get("data", {})
                extract_results = task_data.get("extract_result", [])

                # 检查任务状态
                all_done = True
                any_done = False

                print(f"\n任务状态检查 ({retry_count}/{max_retries}):")
                for i, result in enumerate(extract_results):
                    current_status = result.get("state", "")
                    file_name = result.get("file_name", f"文件{i+1}")

                    status_icon = "✅" if current_status == "done" else "⏳"
                    print(f"  {status_icon} {file_name}: {current_status}")

                    if current_status == "done":
                        any_done = True
                    else:
                        all_done = False

                if all_done:
                    print("\n🎉 所有任务已完成！")
                    return {
                        "success": True,
                        "extract_results": extract_results,
                        "task_data": task_data,
                        "status_data": status_data
                    }

                print(f"等待 {sleep_seconds} 秒后继续检查...")
                time.sleep(sleep_seconds)

            except Exception as e:
                if retry_count < max_retries:
                    print(f"查询出错，{sleep_seconds}秒后重试: {e}")
                    time.sleep(sleep_seconds)
                else:
                    print(f"查询失败: {e}")

        return {
            "success": False,
            "error": "轮询超时，无法获取最终结果"
        }

    def download_results(self, extract_results: list) -> list:
        """
        下载批次任务结果

        Args:
            extract_results: 任务结果列表

        Returns:
            list: 下载的文件信息列表
        """
        downloaded_files = []

        for i, result in enumerate(extract_results):
            if result.get("state") == "done":
                try:
                    file_name = result.get("file_name", f"文件_{i+1}")
                    zip_url = result.get("full_zip_url", "")

                    if not zip_url:
                        print(f"⚠️  {file_name}: 没有下载链接")
                        continue

                    downloaded_file = self.download_zip_file(zip_url, file_name)
                    if downloaded_file:
                        downloaded_files.append(downloaded_file)
                        print(f"✅ {file_name}: 下载并解压完成")
                    else:
                        print(f"❌ {file_name}: 下载失败")

                except Exception as e:
                    print(f"❌ 处理文件时出错: {e}")

        return downloaded_files

    def download_zip_file(self, zip_url: str, file_name: str, max_retries: int = 3) -> dict:
        """
        下载并解压ZIP文件

        Args:
            zip_url: ZIP文件URL
            file_name: 文件名
            max_retries: 最大重试次数

        Returns:
            dict: 包含文件名和解压目录的字典，失败返回None
        """
        import zipfile
        import re

        retry_count = 0

        while retry_count < max_retries:
            try:
                response = requests.get(zip_url, timeout=120)

                if response.status_code == 200:
                    # 生成安全的文件名
                    base_name = Path(file_name).stem
                    safe_name = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', base_name).strip()
                    safe_name = re.sub(r'\s+', '_', safe_name)

                    if not safe_name or safe_name.isdigit():
                        safe_name = f"financial_report_{safe_name}"

                    # 创建输出目录
                    extract_dir = self.output_dir / safe_name
                    extract_dir.mkdir(parents=True, exist_ok=True)

                    # 保存ZIP文件
                    zip_path = extract_dir / f"{safe_name}.zip"
                    with open(zip_path, "wb") as f:
                        f.write(response.content)

                    # 解压ZIP文件
                    try:
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            zip_ref.extractall(extract_dir)

                        # 删除ZIP文件
                        zip_path.unlink()

                        return {
                            "file_name": file_name,
                            "extract_dir": str(extract_dir)
                        }
                    except Exception as e:
                        print(f"解压失败: {e}")
                        return None
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        time.sleep(2)
                    continue

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"下载重试 {retry_count}/{max_retries}: {e}")
                    time.sleep(2)
                else:
                    print(f"下载失败: {e}")

        return None

    def process_complete_workflow(self, pdf_files: list) -> bool:
        """
        执行完整的工作流程：上传 → 等待 → 下载结果

        Args:
            pdf_files: PDF文件列表

        Returns:
            bool: 是否成功
        """
        print("🚀 开始完整的解析流程...")
        print(f"📁 结果将保存到: {self.output_dir}")

        # 步骤1: 上传文件
        print("\n📤 步骤1: 上传PDF文件")
        upload_success = self.upload_and_parse_batch(pdf_files)

        if not upload_success:
            print("❌ 上传失败，流程终止")
            return False

        # 获取最新的批次ID
        batch_files = list(self.output_dir.glob("batch_*.json"))
        if not batch_files:
            print("❌ 找不到批次信息文件")
            return False

        latest_batch = max(batch_files, key=lambda x: x.stat().st_mtime)
        with open(latest_batch, 'r', encoding='utf-8') as f:
            batch_info = json.load(f)
        batch_id = batch_info["batch_id"]

        print(f"\n⏳ 步骤2: 等待解析完成 (批次ID: {batch_id})")
        print("这可能需要几分钟时间，请耐心等待...")
        print("💡 如果等待时间过长，可以使用 continue_download.py 脚本继续下载")

        # 步骤2: 检查任务状态
        status_result = self.check_task_status(batch_id)

        if not status_result.get("success"):
            print(f"❌ 任务状态检查失败: {status_result.get('error')}")
            print(f"💡 可以运行以下命令继续下载:")
            print(f"   python continue_download.py {batch_id}")
            return False

        # 步骤3: 下载结果
        print("\n📥 步骤3: 下载解析结果")
        downloaded_files = self.download_results(status_result.get("extract_results", []))

        if downloaded_files:
            print(f"\n🎉 解析完成！成功处理了 {len(downloaded_files)} 个文件")
            print("结果已保存到以下目录:")
            for file_info in downloaded_files:
                print(f"  📁 {file_info['extract_dir']}")
            return True
        else:
            print("❌ 没有成功下载任何文件")
            return False

    def run(self):
        """运行解析流程"""
        print("=" * 50)
        print("财报PDF解析工具")
        print("=" * 50)
        
        # 查找PDF文件
        pdf_files = self.find_pdf_files()
        
        if not pdf_files:
            return
        
        # 显示文件列表（只显示前10个）
        print("\n找到的PDF文件:")
        display_count = min(10, len(pdf_files))
        for i, pdf_file in enumerate(pdf_files[:display_count], 1):
            print(f"{i:3d}. {pdf_file.name}")

        if len(pdf_files) > display_count:
            print(f"... 还有 {len(pdf_files) - display_count} 个文件")

        # 确认是否继续
        confirm = input(f"\n是否解析这 {len(pdf_files)} 个文件? (y/N): ")
        if confirm.lower() != 'y':
            print("已取消")
            return

        # 执行完整的解析流程
        success = self.process_complete_workflow(pdf_files)

        if success:
            print("\n🎉 所有文件解析完成！")
            print(f"📁 结果保存在: {self.output_dir}")
        else:
            print("\n❌ 解析流程失败，请检查配置和网络连接")

def main():
    """主函数"""
    try:
        parser = FinancialPDFParser()
        parser.run()

    except ValueError as e:
        print(f"配置错误: {e}")
        print("请检查.env文件中的配置")
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()
