"""
批量处理模块
用于批量处理财报数据库中的PDF文件
"""

import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import List, Tuple
from loguru import logger
from tqdm import tqdm

from config import config
from pdf_parser import PDFParser

class BatchProcessor:
    """批量处理器"""
    
    def __init__(self):
        """初始化批量处理器"""
        self.parser = PDFParser()
        self.results = []
    
    def find_pdf_files(self, input_dir: Path = None) -> List[Path]:
        """
        查找输入目录中的所有PDF文件
        
        Args:
            input_dir: 输入目录，如果为None则使用配置中的目录
            
        Returns:
            PDF文件路径列表
        """
        if input_dir is None:
            input_dir = config.input_dir
        
        if not input_dir.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return []
        
        # 查找所有PDF文件
        pdf_files = list(input_dir.glob("*.pdf"))
        pdf_files.extend(input_dir.glob("*.PDF"))  # 支持大写扩展名
        
        # 按文件名排序
        pdf_files.sort(key=lambda x: x.name)
        
        logger.info(f"在 {input_dir} 中找到 {len(pdf_files)} 个PDF文件")
        
        return pdf_files
    
    def process_single_threaded(
        self, 
        pdf_files: List[Path], 
        output_dir: Path = None
    ) -> List[Tuple[str, bool, str]]:
        """
        单线程批量处理PDF文件
        
        Args:
            pdf_files: PDF文件路径列表
            output_dir: 输出目录
            
        Returns:
            处理结果列表
        """
        if output_dir is None:
            output_dir = config.output_dir
        
        results = []
        
        # 使用tqdm显示进度条
        with tqdm(total=len(pdf_files), desc="处理PDF文件") as pbar:
            for pdf_file in pdf_files:
                pbar.set_description(f"处理: {pdf_file.name}")
                
                success, message = self.parser.parse_single_pdf(pdf_file, output_dir)
                results.append((pdf_file.name, success, message))
                
                pbar.update(1)
                
                # 更新进度条后缀信息
                success_count = sum(1 for _, s, _ in results if s)
                pbar.set_postfix({
                    "成功": success_count,
                    "失败": len(results) - success_count
                })
        
        return results
    
    def process_multi_threaded(
        self, 
        pdf_files: List[Path], 
        output_dir: Path = None,
        max_workers: int = None
    ) -> List[Tuple[str, bool, str]]:
        """
        多线程批量处理PDF文件
        
        Args:
            pdf_files: PDF文件路径列表
            output_dir: 输出目录
            max_workers: 最大工作线程数
            
        Returns:
            处理结果列表
        """
        if output_dir is None:
            output_dir = config.output_dir
        
        if max_workers is None:
            max_workers = config.max_workers
        
        results = []
        
        # 使用线程池执行器
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {
                executor.submit(self.parser.parse_single_pdf, pdf_file, output_dir): pdf_file
                for pdf_file in pdf_files
            }
            
            # 使用tqdm显示进度
            with tqdm(total=len(pdf_files), desc="处理PDF文件") as pbar:
                for future in as_completed(future_to_file):
                    pdf_file = future_to_file[future]
                    
                    try:
                        success, message = future.result()
                        results.append((pdf_file.name, success, message))
                    except Exception as e:
                        error_msg = f"处理文件时发生异常: {pdf_file.name} - {str(e)}"
                        logger.exception(error_msg)
                        results.append((pdf_file.name, False, error_msg))
                    
                    pbar.update(1)
                    
                    # 更新进度条后缀信息
                    success_count = sum(1 for _, s, _ in results if s)
                    pbar.set_postfix({
                        "成功": success_count,
                        "失败": len(results) - success_count
                    })
        
        return results
    
    def process_all(
        self, 
        input_dir: Path = None, 
        output_dir: Path = None,
        use_multithread: bool = True
    ) -> List[Tuple[str, bool, str]]:
        """
        处理所有PDF文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            use_multithread: 是否使用多线程
            
        Returns:
            处理结果列表
        """
        # 查找PDF文件
        pdf_files = self.find_pdf_files(input_dir)
        
        if not pdf_files:
            logger.warning("没有找到PDF文件")
            return []
        
        # 创建输出目录
        if output_dir is None:
            output_dir = config.output_dir
        output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"开始批量处理 {len(pdf_files)} 个PDF文件")
        logger.info(f"输出目录: {output_dir}")
        logger.info(f"使用多线程: {use_multithread}")
        
        start_time = time.time()
        
        # 选择处理方式
        if use_multithread and config.max_workers > 1:
            results = self.process_multi_threaded(pdf_files, output_dir)
        else:
            results = self.process_single_threaded(pdf_files, output_dir)
        
        elapsed_time = time.time() - start_time
        
        # 统计结果
        success_count = sum(1 for _, success, _ in results if success)
        failed_count = len(results) - success_count
        
        logger.info(f"批量处理完成!")
        logger.info(f"总文件数: {len(results)}")
        logger.info(f"成功: {success_count}")
        logger.info(f"失败: {failed_count}")
        logger.info(f"总耗时: {elapsed_time:.2f}秒")
        logger.info(f"平均耗时: {elapsed_time/len(results):.2f}秒/文件")
        
        # 保存结果
        self.results = results
        self.save_results_summary(output_dir)
        
        return results
    
    def save_results_summary(self, output_dir: Path):
        """
        保存处理结果摘要
        
        Args:
            output_dir: 输出目录
        """
        try:
            summary_file = output_dir / "processing_summary.txt"
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("财报PDF批量处理结果摘要\n")
                f.write("=" * 50 + "\n\n")
                
                # 统计信息
                success_count = sum(1 for _, success, _ in self.results if success)
                failed_count = len(self.results) - success_count
                
                f.write(f"总文件数: {len(self.results)}\n")
                f.write(f"成功处理: {success_count}\n")
                f.write(f"处理失败: {failed_count}\n")
                f.write(f"成功率: {success_count/len(self.results)*100:.1f}%\n\n")
                
                # 详细结果
                f.write("详细结果:\n")
                f.write("-" * 50 + "\n")
                
                for filename, success, message in self.results:
                    status = "✓" if success else "✗"
                    f.write(f"{status} {filename}: {message}\n")
                
                # 失败文件列表
                failed_files = [filename for filename, success, _ in self.results if not success]
                if failed_files:
                    f.write("\n失败文件列表:\n")
                    f.write("-" * 50 + "\n")
                    for filename in failed_files:
                        f.write(f"- {filename}\n")
            
            logger.info(f"处理结果摘要已保存到: {summary_file}")
            
        except Exception as e:
            logger.exception(f"保存结果摘要失败: {e}")
    
    def get_failed_files(self) -> List[str]:
        """获取处理失败的文件列表"""
        return [filename for filename, success, _ in self.results if not success]
    
    def get_success_files(self) -> List[str]:
        """获取处理成功的文件列表"""
        return [filename for filename, success, _ in self.results if success]
